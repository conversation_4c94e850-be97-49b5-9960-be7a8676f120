{"kind": "collectionType", "collectionName": "business_partners", "info": {"singularName": "business-partner", "pluralName": "business-partners", "displayName": "Business Partner", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"bp_id": {"type": "string", "unique": true, "required": true, "column": {"unique": true}}, "bp_category": {"type": "string"}, "bp_full_name": {"type": "string"}, "bp_grouping": {"type": "string"}, "bp_uuid": {"type": "string"}, "org_bp_name1": {"type": "string"}, "org_bp_name2": {"type": "string"}, "org_bp_name3": {"type": "string"}, "org_bp_name4": {"type": "string"}, "search_term1": {"type": "string"}, "search_term2": {"type": "string"}, "gender_code_name": {"type": "string"}, "group_business_partner_name1": {"type": "string"}, "group_business_partner_name2": {"type": "string"}, "independent_address_id": {"type": "string"}, "industry": {"type": "string"}, "international_location_number1": {"type": "string"}, "international_location_number2": {"type": "string"}, "international_location_number3": {"type": "string"}, "is_female": {"type": "boolean"}, "is_male": {"type": "boolean"}, "is_natural_person": {"type": "boolean"}, "is_sex_unknown": {"type": "boolean"}, "is_marked_for_archiving": {"type": "boolean", "default": false}, "language": {"type": "string"}, "last_change_date": {"type": "datetime"}, "last_changed_by_user": {"type": "string"}, "last_change_time": {"type": "datetime"}, "last_name": {"type": "string"}, "legal_form": {"type": "string"}, "middle_name": {"type": "string"}, "name_country": {"type": "string"}, "name_format": {"type": "string"}, "organization_foundation_date": {"type": "datetime"}, "organization_liquidation_date": {"type": "datetime"}, "person_full_name": {"type": "string"}, "person_number": {"type": "string"}, "trading_partner": {"type": "string"}, "business_partner_print_format": {"type": "string"}, "business_partner_occupation": {"type": "string"}, "bus_part_marital_status": {"type": "string"}, "bus_part_nationality": {"type": "string"}, "business_partner_birth_name": {"type": "string"}, "business_partner_supplement_name": {"type": "string"}, "natural_person_employer_name": {"type": "string"}, "last_name_prefix": {"type": "string"}, "last_name_second_prefix": {"type": "string"}, "initials": {"type": "string"}, "bp_data_controller_is_not_required": {"type": "boolean"}, "business_partner_id_by_ext_system": {"type": "string"}, "correspondence_language": {"type": "string"}, "created_by_user": {"type": "string"}, "creation_date": {"type": "datetime"}, "creation_time": {"type": "datetime"}, "etag": {"type": "string"}, "first_name": {"type": "string"}, "form_of_address": {"type": "string"}, "addresses": {"type": "relation", "relation": "oneToMany", "target": "api::business-partner-address.business-partner-address", "mappedBy": "business_partner"}, "credit_worthiness": {"type": "relation", "relation": "oneToOne", "target": "api::bp-credit-worthiness.bp-credit-worthiness", "mappedBy": "business_partner"}, "bp_intl_address_versions": {"type": "relation", "relation": "oneToMany", "target": "api::bp-intl-address-version.bp-intl-address-version", "mappedBy": "business_partner"}, "intl_loc_number": {"type": "relation", "relation": "oneToOne", "target": "api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number", "mappedBy": "business_partner"}, "address_usages": {"type": "relation", "relation": "oneToMany", "target": "api::bp-address-usage.bp-address-usage", "mappedBy": "business_partner"}, "roles": {"type": "relation", "relation": "oneToMany", "target": "api::business-partner-role.business-partner-role", "mappedBy": "business_partner"}, "payment_cards": {"type": "relation", "relation": "oneToMany", "target": "api::business-partner-payment-card.business-partner-payment-card", "mappedBy": "business_partner"}, "banks": {"type": "relation", "relation": "oneToMany", "target": "api::business-partner-bank.business-partner-bank", "mappedBy": "business_partner"}, "contact_persons": {"type": "relation", "relation": "oneToMany", "target": "api::business-partner-contact.business-partner-contact", "mappedBy": "business_partner_person"}, "contact_companies": {"type": "relation", "relation": "oneToMany", "target": "api::business-partner-contact.business-partner-contact", "mappedBy": "business_partner_company"}, "contact_person_addresses": {"type": "relation", "relation": "oneToMany", "target": "api::bp-contact-to-address.bp-contact-to-address", "mappedBy": "business_partner_person"}, "contact_company_addresses": {"type": "relation", "relation": "oneToMany", "target": "api::bp-contact-to-address.bp-contact-to-address", "mappedBy": "business_partner_company"}, "contact_person_func_and_depts": {"type": "relation", "relation": "oneToMany", "target": "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept", "mappedBy": "business_partner_person"}, "contact_company_func_and_depts": {"type": "relation", "relation": "oneToMany", "target": "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept", "mappedBy": "business_partner_company"}, "customer": {"type": "relation", "relation": "oneToOne", "target": "api::customer.customer", "mappedBy": "business_partner"}, "supplier": {"type": "relation", "relation": "oneToOne", "target": "api::supplier.supplier", "mappedBy": "business_partner"}, "cb_memberships": {"type": "relation", "relation": "oneToMany", "target": "api::fg-customer-business.fg-customer-business", "mappedBy": "membership"}, "customer_businesses": {"type": "relation", "relation": "oneToMany", "target": "api::fg-customer-business.fg-customer-business", "mappedBy": "business_partner"}, "customer_internals": {"type": "relation", "relation": "oneToMany", "target": "api::fg-customer-internal.fg-customer-internal", "mappedBy": "business_partner"}, "product_businesses": {"type": "relation", "relation": "oneToMany", "target": "api::fg-product-business.fg-product-business", "mappedBy": "vendor"}, "relationships1": {"type": "relation", "relation": "oneToMany", "target": "api::business-partner-relationship.business-partner-relationship", "mappedBy": "business_partner1"}, "relationships2": {"type": "relation", "relation": "oneToMany", "target": "api::business-partner-relationship.business-partner-relationship", "mappedBy": "business_partner2"}, "user_vendors": {"type": "relation", "relation": "oneToMany", "target": "api::user-vendor.user-vendor", "mappedBy": "vendor"}, "identifications": {"type": "relation", "relation": "oneToMany", "target": "api::business-partner-identification.business-partner-identification", "mappedBy": "business_partner"}, "marketing_attributes": {"type": "relation", "relation": "oneToOne", "target": "api::bp-marketing-attribute.bp-marketing-attribute", "mappedBy": "business_partner"}, "bp_extension": {"type": "relation", "relation": "oneToOne", "target": "api::business-partner-extension.business-partner-extension", "mappedBy": "business_partner"}, "notes": {"type": "relation", "relation": "oneToMany", "target": "api::crm-note.crm-note", "mappedBy": "business_partner"}, "activities": {"type": "relation", "relation": "oneToMany", "target": "api::crm-activity.crm-activity", "mappedBy": "business_partner"}, "contact_activities": {"type": "relation", "relation": "oneToMany", "target": "api::crm-activity.crm-activity", "mappedBy": "business_partner_contact"}, "owner_activities": {"type": "relation", "relation": "oneToMany", "target": "api::crm-activity.crm-activity", "mappedBy": "business_partner_owner"}, "organizer_activities": {"type": "relation", "relation": "oneToMany", "target": "api::crm-activity.crm-activity", "mappedBy": "business_partner_organizer"}, "processor_activities": {"type": "relation", "relation": "oneToMany", "target": "api::crm-activity.crm-activity", "mappedBy": "business_partner_processor"}, "employee_activities": {"type": "relation", "relation": "oneToMany", "target": "api::crm-activity.crm-activity", "mappedBy": "business_partner_employee"}, "involved_parties": {"type": "relation", "relation": "oneToMany", "target": "api::crm-involved-party.crm-involved-party", "mappedBy": "business_partner"}, "opportunities": {"type": "relation", "relation": "oneToMany", "target": "api::crm-opportunity.crm-opportunity", "mappedBy": "business_partner"}, "contact_opportunities": {"type": "relation", "relation": "oneToMany", "target": "api::crm-opportunity.crm-opportunity", "mappedBy": "primary_contact"}, "owner_opportunities": {"type": "relation", "relation": "oneToMany", "target": "api::crm-opportunity.crm-opportunity", "mappedBy": "business_partner_owner"}, "bp_attachments": {"type": "relation", "relation": "oneToMany", "target": "api::crm-attachment.crm-attachment", "mappedBy": "business_partner"}, "partner_functions": {"type": "relation", "relation": "oneToMany", "target": "api::customer-partner-function.customer-partner-function", "mappedBy": "business_partner"}, "opportunity_parties": {"type": "relation", "relation": "oneToMany", "target": "api::crm-opportunity-party.crm-opportunity-party", "mappedBy": "business_partner"}, "opportunity_contact_parties": {"type": "relation", "relation": "oneToMany", "target": "api::crm-opportunity-party-contact-party.crm-opportunity-party-contact-party", "mappedBy": "business_partner"}, "opportunity_sales_team_parties": {"type": "relation", "relation": "oneToMany", "target": "api::crm-opportunity-sales-team-party.crm-opportunity-sales-team-party", "mappedBy": "business_partner"}, "crm_competitor_products": {"type": "relation", "relation": "oneToMany", "target": "api::crm-competitor-product.crm-competitor-product", "mappedBy": "business_partner"}, "crm_org_unit_employees": {"type": "relation", "relation": "oneToMany", "target": "api::crm-organisational-unit-employee.crm-organisational-unit-employee", "mappedBy": "business_partner"}, "crm_org_unit_managers": {"type": "relation", "relation": "oneToMany", "target": "api::crm-organisational-unit-manager.crm-organisational-unit-manager", "mappedBy": "business_partner"}, "admin_user": {"type": "relation", "relation": "oneToOne", "target": "admin::user"}}}