import { Context } from "koa";
import jwt from "jsonwebtoken";

async function getAdminUser(ctx: Context) {
  const authHeader = ctx.request.header.authorization;

  if (!authHeader) return ctx.unauthorized("Missing Authorization header");

  const token = authHeader.replace("Bearer ", "");

  try {
    const decoded = jwt.verify(token, process.env.ADMIN_JWT_SECRET || "");

    const adminUser = await strapi.db.query("admin::user").findOne({
      where: { id: decoded["id"] },
      populate: ["roles"],
    });

    if (!adminUser) return ctx.unauthorized("Invalid admin user");
    return adminUser;
  } catch (err: any) {
    return ctx.unauthorized(`Invalid token: ${err.message}`);
  }
}

async function checkAdminPermissions(moduleUID: string) {
  return async (ctx: any, next: any) => {
    const admin = await getAdminUser(ctx);
    if (!admin) return ctx.unauthorized("Invalid admin");
    ctx.state.admin = admin;

    const method = ctx.request.method.toUpperCase();
    const requiredPermission = {
      GET: "canRead",
      POST: "canCreate",
      PUT: "canUpdate",
      PATCH: "canUpdate",
      DELETE: "canDelete",
    }[method];

    if (!requiredPermission) {
      return ctx.forbidden("Unsupported HTTP method");
    }

    const isSuperAdmin = ctx.state.admin.roles.some(
      (role: any) => role.code === "strapi-super-admin"
    );
    if (isSuperAdmin) return await next();

    let where = {
      moduleUID,
      role: {
        id: {
          $in: admin?.roles?.map((role: any) => role.id),
        },
      },
    };

    if (requiredPermission) {
      where[requiredPermission] = true;
    }

    const hasPermission = await strapi
      .query("api::permission-api-bridge.permission-api-bridge")
      .findOne({ where });

    if (!hasPermission) {
      return ctx.forbidden(`No ${requiredPermission} permission available`);
    }

    return await next();
  };
}

function sanitizeAdminFields(user: any) {
  if (!user || typeof user !== "object") return user;

  const { password, resetPasswordToken, registrationToken, ...safeUser } = user;

  return safeUser;
}

function sanitizeDocument(doc: any) {
  if (Array.isArray(doc)) {
    return doc.map(sanitizeDocument);
  }

  if (typeof doc !== "object" || doc === null) {
    return doc;
  }

  const sanitized = { ...doc };

  if (sanitized.createdBy) {
    sanitized.createdBy = sanitizeAdminFields(sanitized.createdBy);
  }

  if (sanitized.updatedBy) {
    sanitized.updatedBy = sanitizeAdminFields(sanitized.updatedBy);
  }

  // Recursively sanitize nested relations
  for (const key in sanitized) {
    if (typeof sanitized[key] === "object") {
      sanitized[key] = sanitizeDocument(sanitized[key]);
    }
  }

  return sanitized;
}

function findManyDocument(uid: string) {
  return async (ctx: Context) => {
    try {
      const {
        locale,
        status,
        filters,
        fields,
        populate,
        pagination = {},
        sort,
      }: any = ctx.query;
      const { page = 1, pageSize = 25 } = pagination as any;

      const start = (Number(page) - 1) * Number(pageSize);
      const limit = Number(pageSize);

      // @ts-ignore
      const doc: any = strapi.documents(uid);

      const documents = await doc.findMany({
        locale,
        status,
        filters,
        fields,
        populate,
        sort,
        start,
        limit,
      });

      const total = await doc.count({ filters });

      const data = sanitizeDocument(documents);

      return ctx.send({
        data,
        meta: {
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total,
          },
        },
      });
    } catch (err) {
      console.log("Bridge Error", err);
      return ctx.internalServerError("Bridge failed");
    }
  };
}

function findOneDocument(uid: string) {
  return async (ctx: Context) => {
    try {
      const { id } = ctx.params;
      const { locale, status, populate, fields }: any = ctx.query;

      let docId = id;

      if (/^\d+$/.test(id)) {
        const { documentId } = await strapi
          .query(uid)
          .findOne({ where: { id } });
        docId = documentId;
      }

      // @ts-ignore
      const doc: any = strapi.documents(uid);
      const documents = await doc.findOne({
        documentId: docId,
        locale,
        status,
        fields,
        populate,
      });

      const data = sanitizeDocument(documents);

      return ctx.send({
        data,
        meta: {},
      });
    } catch (err) {
      console.log("Bridge Error", err);
      return ctx.internalServerError("Bridge failed");
    }
  };
}

async function registerDBLifecycles(uid: string) {
  strapi.db.lifecycles.subscribe({
    models: [uid],
    async beforeCreate(event) {
      const { params } = event;
      // Access the request context
      const ctx = strapi.requestContext.get();
      if (ctx && ctx?.state && ctx?.state?.admin) {
        params.data.createdBy = ctx?.state?.admin?.id;
        params.data.updatedBy = ctx?.state?.admin?.id;
      }
    },
    async beforeUpdate(event) {
      const { params } = event;
      // Access the request context
      const ctx = strapi.requestContext.get();
      if (ctx && ctx?.state && ctx?.state?.admin) {
        params.data.updatedBy = ctx?.state?.admin?.id;
      }
    },
  });
}

async function manageAPIBridgePermissions(specificRoles: any[] | null = null) {
  const BATCH_SIZE = 100;
  const modules: any[] = [];

  // -----------------------------
  // ✅ Core Modules
  // -----------------------------
  for (const apiName in strapi.apis) {
    const api: any = strapi.api(apiName);
    const contentType = api.contentType(apiName);
    const uid = contentType?.uid;

    const moduleUID = uid || apiName;
    const moduleName = contentType?.info?.displayName || apiName;
    if (!modules.find((m: any) => m.id === moduleUID)) {
      modules.push({ id: moduleUID, name: moduleName });
    }
  }

  // -----------------------------
  // ✅ Plugin Modules
  // -----------------------------
  for (const pluginName in strapi.plugins) {
    const plugin = strapi.plugin(pluginName);

    if (!["users-permissions"].includes(pluginName)) continue;

    const contentAPI = plugin.routes?.["content-api"];

    if (!contentAPI?.routes || !Array.isArray(contentAPI.routes)) continue;

    for (const route of contentAPI.routes) {
      const handler: any = route.handler;
      if (typeof handler !== "string") continue;

      const [controllerName, fn] = handler.split(".");
      const uid = `plugin::${pluginName}.${controllerName}`;
      if (!modules.find((m: any) => m.id === uid)) {
        modules.push({
          id: uid,
          name: `${pluginName} ${controllerName}`,
        });
      }
    }
  }

  // -----------------------------
  // ✅ Roles to Work With
  // -----------------------------
  const roles = specificRoles
    ? specificRoles
    : await strapi.query("admin::role").findMany({
        where: {
          code: { $not: "strapi-super-admin" },
        },
      });

  if (!roles.length) return;

  // -----------------------------
  // ✅ Existing Permission Entries
  // -----------------------------
  const existingPermissions = await strapi.db
    .query("api::permission-api-bridge.permission-api-bridge")
    .findMany({
      populate: ["role"],
    });

  const existingPairs = new Set(
    existingPermissions.map((p) => `${p.role.id}:${p.moduleUID}`)
  );

  // -----------------------------
  // ✅ Create Missing Permission Pairs
  // -----------------------------
  const permissionsToCreate = [];

  for (const role of roles) {
    if (role.code === "strapi-super-admin") continue;

    for (const module of modules) {
      const pairKey = `${role.id}:${module.id}`;
      if (!existingPairs.has(pairKey)) {
        const isAdmin = role.code.includes("admin");
        permissionsToCreate.push({
          role: role.id,
          moduleUID: module.id,
          module: module.name,
          canRead: isAdmin || !module.id.includes("plugin::"),
          canCreate: isAdmin,
          canUpdate: isAdmin,
          canDelete: isAdmin,
        });
      }
    }
  }

  // -----------------------------
  // ✅ Create in Batches
  // -----------------------------
  for (let i = 0; i < permissionsToCreate.length; i += BATCH_SIZE) {
    const batch = permissionsToCreate.slice(i, i + BATCH_SIZE);

    await Promise.all(
      batch.map((permission) =>
        strapi.db
          .query("api::permission-api-bridge.permission-api-bridge")
          .create({
            data: {
              moduleUID: permission.moduleUID,
              module: permission.module,
              canRead: permission.canRead,
              canCreate: permission.canCreate,
              canUpdate: permission.canUpdate,
              canDelete: permission.canDelete,
              role: { connect: [permission.role] },
            },
          })
      )
    );

    strapi.log.info(
      `✅ Created batch ${Math.floor(i / BATCH_SIZE) + 1} with ${
        batch.length
      } records`
    );
  }

  strapi.log.info(
    `✅ All ${permissionsToCreate.length} permissions processed successfully`
  );

  // -----------------------------
  // ✅ Cleanup: Remove Deleted Modules (Only on full sync)
  // -----------------------------
  if (!specificRoles) {
    const permissionsToDelete = existingPermissions.filter(
      (p) => !modules.find((m: any) => m.id === p.moduleUID)
    );

    if (permissionsToDelete.length > 0) {
      await strapi.db
        .query("api::permission-api-bridge.permission-api-bridge")
        .deleteMany({
          where: { id: { $in: permissionsToDelete.map((p) => p.id) } },
        });

      strapi.log.info(
        `🗑️ Deleted ${permissionsToDelete.length} permissions for removed modules`
      );
    }
  }
}

export {
  checkAdminPermissions,
  findManyDocument,
  findOneDocument,
  registerDBLifecycles,
  manageAPIBridgePermissions,
};
