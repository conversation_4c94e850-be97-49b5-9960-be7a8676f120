import { isEmpty, merge } from "lodash/fp";

// Type definitions
interface ModelAttribute {
  type: string;
  component?: string;
  components?: string[];
  target?: string;
  related?: any;
}

interface Model {
  uid: string;
  attributes: Record<string, ModelAttribute>;
  collectionName: string;
}

interface PopulateObject {
  populate?: Record<string, any>;
  on?: Record<string, any>;
}

// Strapi interface for the methods we use
interface StrapiInstance {
  getModel: (uid: string) => Model;
  plugin: (name: string) => {
    config: (key: string) => any;
  } | undefined;
}

// Type assertion for the global strapi object
declare const strapi: StrapiInstance;

const getModelPopulationAttributes = (model: Model): Record<string, ModelAttribute> => {
  if (model.uid === "plugin::upload.file") {
    const { related, ...attributes } = model.attributes;
    return attributes;
  }

  return model.attributes;
};

const getFullPopulateObject = (
  modelUid: string,
  maxDepth: number = 20,
  ignore?: string[]
): boolean | PopulateObject | undefined => {
  const skipCreatorFields = strapi
      .plugin("strapi-plugin-populate-deep")
      ?.config("skipCreatorFields");

  if (maxDepth <= 1) {
    return true;
  }
  if (modelUid === "admin::user" && skipCreatorFields) {
    return undefined;
  }

  const populate: Record<string, any> = {};
  const model = strapi.getModel(modelUid);
  if (ignore && !ignore.includes(model.collectionName)) {
    ignore.push(model.collectionName);
  }

  for (const [key, value] of Object.entries(
      getModelPopulationAttributes(model)
  )) {
    if (ignore?.includes(key)) continue;
    if (value) {
      if (value.type === "component") {
        populate[key] = getFullPopulateObject(value.component!, maxDepth - 1, ignore);
      } else if (value.type === "dynamiczone") {
        const dynamicPopulate = value.components!.reduce((prev: Record<string, any>, cur: string) => {
          const curPopulate = getFullPopulateObject(cur, maxDepth - 1, ignore);
          return merge(prev, {[cur]: curPopulate});
        }, {});
        populate[key] = isEmpty(dynamicPopulate) ? true : { on: dynamicPopulate };
      } else if (value.type === "relation") {
        const relationPopulate = getFullPopulateObject(
            value.target!,
            key === "localizations" && maxDepth > 2 ? 1 : maxDepth - 1,
            ignore
        );
        if (relationPopulate) {
          populate[key] = relationPopulate;
        }
      } else if (value.type === "media") {
        populate[key] = true;
      }
    }
  }
  return isEmpty(populate) ? true : { populate };
};

export {
  getFullPopulateObject,
};
